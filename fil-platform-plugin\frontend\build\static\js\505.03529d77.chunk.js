"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[505],{505:(e,s,r)=>{r.r(s),r.d(s,{default:()=>x});var a=r(5043),t=r(3519),o=r(1072),l=r(8602),n=r(8628),c=r(4282),i=r(4117),d=r(1283),m=r(4312),h=r(579);const x=()=>{const{t:e}=(0,i.Bd)(),[s,r]=(0,a.useState)(null),[x,f]=(0,a.useState)(!0),[j,g]=(0,a.useState)({totalTechCommission:0,totalOpsCommission:0,memberCount:0,totalStorage:0,soldStorage:0,remainingStorage:0});return(0,a.useEffect)(()=>{(async()=>{const e=(0,m.b)();if(!e)return;f(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return console.log("No user found"),void f(!1);console.log("Current user:",s.id),console.log("User role from localStorage:",localStorage.getItem("user_role"));const{data:a,error:t}=await e.from("agent_profiles").select("*").eq("user_id",s.id).single();if(t)return console.error("Error fetching agent profile:",t),console.error("User ID:",s.id),console.error("Error details:",t),void f(!1);console.log("Agent profile data:",a),r(a),await(async(e,s,r)=>{try{const{data:a,error:t}=await e.from("customer_profiles").select("user_id",{count:"exact"}).eq("agent_id",s),{data:o,error:l}=await e.from("orders").select("\n                    id,\n                    shares,\n                    storage_cost,\n                    pledge_cost,\n                    tech_fee_pct,\n                    ops_fee_pct,\n                    products (\n                        total_shares,\n                        sold_shares,\n                        tech_commission_pct,\n                        ops_commission_pct,\n                        maker_id\n                    )\n                ").eq("agent_id",s),{data:n,error:c}=await e.from("products").select("total_shares, sold_shares").eq("maker_id",r);let i={totalTechCommission:0,totalOpsCommission:0,memberCount:a?a.length:0,totalStorage:0,soldStorage:0,remainingStorage:0};o&&!l&&o.forEach(e=>{const s=(e.storage_cost||0)*(e.tech_fee_pct||0)/100,r=(e.storage_cost||0)*(e.ops_fee_pct||0)/100;i.totalTechCommission+=s,i.totalOpsCommission+=r}),n&&!c&&(n.forEach(e=>{i.totalStorage+=e.total_shares||0,i.soldStorage+=e.sold_shares||0}),i.remainingStorage=i.totalStorage-i.soldStorage),g(i)}catch(a){console.error("Error fetching dashboard stats:",a)}})(e,s.id,a.maker_id),f(!1)})()},[]),x?(0,h.jsx)("div",{children:e("loading_agent_dashboard")}):s?(0,h.jsxs)(t.A,{fluid:!0,children:[(0,h.jsx)(o.A,{className:"mb-3",children:(0,h.jsx)(l.A,{children:(0,h.jsx)("h2",{children:e("agent_dashboard")})})}),(0,h.jsxs)(o.A,{children:[(0,h.jsx)(l.A,{md:4,children:(0,h.jsx)(n.A,{className:"bg-primary text-white mb-3",children:(0,h.jsxs)(n.A.Body,{children:[(0,h.jsx)(n.A.Title,{children:e("brand_name")}),(0,h.jsx)("h3",{children:s.brand_name||"N/A"})]})})}),(0,h.jsx)(l.A,{md:4,children:(0,h.jsx)(n.A,{className:"bg-success text-white mb-3",children:(0,h.jsxs)(n.A.Body,{children:[(0,h.jsx)(n.A.Title,{children:e("commission_rate")}),(0,h.jsx)("h3",{children:s.commission_pct?100*s.commission_pct+"%":"N/A"})]})})}),(0,h.jsx)(l.A,{md:4,children:(0,h.jsx)(n.A,{className:"bg-info text-white mb-3",children:(0,h.jsxs)(n.A.Body,{children:[(0,h.jsx)(n.A.Title,{children:e("kyc_status")}),(0,h.jsx)("h3",{children:s.kyc_status||"N/A"})]})})})]}),(0,h.jsxs)(o.A,{children:[(0,h.jsx)(l.A,{md:6,children:(0,h.jsx)(n.A,{className:"bg-warning text-white mb-3",children:(0,h.jsxs)(n.A.Body,{children:[(0,h.jsx)(n.A.Title,{children:e("total_tech_commission")}),(0,h.jsxs)("h3",{children:[j.totalTechCommission.toFixed(6)," FIL"]})]})})}),(0,h.jsx)(l.A,{md:6,children:(0,h.jsx)(n.A,{className:"bg-danger text-white mb-3",children:(0,h.jsxs)(n.A.Body,{children:[(0,h.jsx)(n.A.Title,{children:e("total_ops_commission")}),(0,h.jsxs)("h3",{children:[j.totalOpsCommission.toFixed(6)," FIL"]})]})})})]}),(0,h.jsxs)(o.A,{children:[(0,h.jsx)(l.A,{md:3,children:(0,h.jsx)(n.A,{className:"bg-secondary text-white mb-3",children:(0,h.jsxs)(n.A.Body,{children:[(0,h.jsx)(n.A.Title,{children:e("member_count")}),(0,h.jsx)("h3",{children:j.memberCount})]})})}),(0,h.jsx)(l.A,{md:3,children:(0,h.jsx)(n.A,{className:"bg-dark text-white mb-3",children:(0,h.jsxs)(n.A.Body,{children:[(0,h.jsx)(n.A.Title,{children:e("total_storage")}),(0,h.jsxs)("h3",{children:[j.totalStorage.toFixed(2)," TiB"]})]})})}),(0,h.jsx)(l.A,{md:3,children:(0,h.jsx)(n.A,{className:"bg-success text-white mb-3",children:(0,h.jsxs)(n.A.Body,{children:[(0,h.jsx)(n.A.Title,{children:e("sold_storage")}),(0,h.jsxs)("h3",{children:[j.soldStorage.toFixed(2)," TiB"]})]})})}),(0,h.jsx)(l.A,{md:3,children:(0,h.jsx)(n.A,{className:"bg-light text-dark mb-3",children:(0,h.jsxs)(n.A.Body,{children:[(0,h.jsx)(n.A.Title,{children:e("remaining_storage")}),(0,h.jsxs)("h3",{children:[j.remainingStorage.toFixed(2)," TiB"]})]})})})]}),(0,h.jsxs)(o.A,{className:"mt-4",children:[(0,h.jsx)(l.A,{md:6,className:"text-center",children:(0,h.jsx)(n.A,{children:(0,h.jsxs)(n.A.Body,{children:[(0,h.jsx)("h4",{children:e("member_management")}),(0,h.jsx)("p",{children:e("my_subordinate_members")}),(0,h.jsx)(c.A,{as:d.N_,to:"/agent/members",variant:"primary",children:e("enter_member_list")})]})})}),(0,h.jsx)(l.A,{md:6,className:"text-center",children:(0,h.jsx)(n.A,{children:(0,h.jsxs)(n.A.Body,{children:[(0,h.jsx)("h4",{children:e("product_management")}),(0,h.jsx)("p",{children:e("products_on_sale")}),(0,h.jsx)(c.A,{as:d.N_,to:"/agent/products",variant:"success",children:e("browse_agent_products")})]})})})]})]}):(0,h.jsx)("div",{className:"alert alert-warning",children:e("not_agent")})}},1072:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8139),t=r.n(a),o=r(5043),l=r(7852),n=r(579);const c=o.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:o="div",...c}=e;const i=(0,l.oU)(r,"row"),d=(0,l.gy)(),m=(0,l.Jm)(),h=`${i}-cols`,x=[];return d.forEach(e=>{const s=c[e];let r;delete c[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==m?`-${e}`:"";null!=r&&x.push(`${h}${a}-${r}`)}),(0,n.jsx)(o,{ref:s,...c,className:t()(a,i,...x)})});c.displayName="Row";const i=c},8602:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8139),t=r.n(a),o=r(5043),l=r(7852),n=r(579);const c=o.forwardRef((e,s)=>{const[{className:r,...a},{as:o="div",bsPrefix:c,spans:i}]=function(e){let{as:s,bsPrefix:r,className:a,...o}=e;r=(0,l.oU)(r,"col");const n=(0,l.gy)(),c=(0,l.Jm)(),i=[],d=[];return n.forEach(e=>{const s=o[e];let a,t,l;delete o[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:l}=s):a=s;const n=e!==c?`-${e}`:"";a&&i.push(!0===a?`${r}${n}`:`${r}${n}-${a}`),null!=l&&d.push(`order${n}-${l}`),null!=t&&d.push(`offset${n}-${t}`)}),[{...o,className:t()(a,...i,...d)},{as:s,bsPrefix:r,spans:i}]}(e);return(0,n.jsx)(o,{...a,ref:s,className:t()(r,!i.length&&c)})});c.displayName="Col";const i=c},8628:(e,s,r)=>{r.d(s,{A:()=>P});var a=r(8139),t=r.n(a),o=r(5043),l=r(7852),n=r(579);const c=o.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:o="div",...c}=e;return a=(0,l.oU)(a,"card-body"),(0,n.jsx)(o,{ref:s,className:t()(r,a),...c})});c.displayName="CardBody";const i=c,d=o.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:o="div",...c}=e;return a=(0,l.oU)(a,"card-footer"),(0,n.jsx)(o,{ref:s,className:t()(r,a),...c})});d.displayName="CardFooter";const m=d;var h=r(1778);const x=o.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:c="div",...i}=e;const d=(0,l.oU)(r,"card-header"),m=(0,o.useMemo)(()=>({cardHeaderBsPrefix:d}),[d]);return(0,n.jsx)(h.A.Provider,{value:m,children:(0,n.jsx)(c,{ref:s,...i,className:t()(a,d)})})});x.displayName="CardHeader";const f=x,j=o.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:o,as:c="img",...i}=e;const d=(0,l.oU)(r,"card-img");return(0,n.jsx)(c,{ref:s,className:t()(o?`${d}-${o}`:d,a),...i})});j.displayName="CardImg";const g=j,u=o.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:o="div",...c}=e;return a=(0,l.oU)(a,"card-img-overlay"),(0,n.jsx)(o,{ref:s,className:t()(r,a),...c})});u.displayName="CardImgOverlay";const b=u,_=o.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:o="a",...c}=e;return a=(0,l.oU)(a,"card-link"),(0,n.jsx)(o,{ref:s,className:t()(r,a),...c})});_.displayName="CardLink";const A=_;var N=r(4488);const p=(0,N.A)("h6"),y=o.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:o=p,...c}=e;return a=(0,l.oU)(a,"card-subtitle"),(0,n.jsx)(o,{ref:s,className:t()(r,a),...c})});y.displayName="CardSubtitle";const w=y,v=o.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:o="p",...c}=e;return a=(0,l.oU)(a,"card-text"),(0,n.jsx)(o,{ref:s,className:t()(r,a),...c})});v.displayName="CardText";const C=v,S=(0,N.A)("h5"),T=o.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:o=S,...c}=e;return a=(0,l.oU)(a,"card-title"),(0,n.jsx)(o,{ref:s,className:t()(r,a),...c})});T.displayName="CardTitle";const $=T,B=o.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:o,text:c,border:d,body:m=!1,children:h,as:x="div",...f}=e;const j=(0,l.oU)(r,"card");return(0,n.jsx)(x,{ref:s,...f,className:t()(a,j,o&&`bg-${o}`,c&&`text-${c}`,d&&`border-${d}`),children:m?(0,n.jsx)(i,{children:h}):h})});B.displayName="Card";const P=Object.assign(B,{Img:g,Title:$,Subtitle:w,Body:i,Link:A,Text:C,Header:f,Footer:m,ImgOverlay:b})}}]);
//# sourceMappingURL=505.03529d77.chunk.js.map