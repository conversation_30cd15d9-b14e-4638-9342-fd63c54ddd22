"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[462],{1072:(e,s,r)=>{r.d(s,{A:()=>d});var a=r(8139),n=r.n(a),l=r(5043),t=r(7852),i=r(579);const c=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:l="div",...c}=e;const d=(0,t.oU)(r,"row"),o=(0,t.gy)(),h=(0,t.Jm)(),x=`${d}-cols`,m=[];return o.forEach(e=>{const s=c[e];let r;delete c[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==h?`-${e}`:"";null!=r&&m.push(`${x}${a}-${r}`)}),(0,i.jsx)(l,{ref:s,...c,className:n()(a,d,...m)})});c.displayName="Row";const d=c},1462:(e,s,r)=>{r.r(s),r.d(s,{default:()=>N});var a=r(5043),n=r(3519),l=r(8139),t=r.n(l),i=r(7852),c=r(579);const d=a.forwardRef((e,s)=>{let{bsPrefix:r,variant:a,animation:n="border",size:l,as:d="div",className:o,...h}=e;r=(0,i.oU)(r,"spinner");const x=`${r}-${n}`;return(0,c.jsx)(d,{ref:s,...h,className:t()(o,x,l&&`${x}-${l}`,a&&`text-${a}`)})});d.displayName="Spinner";const o=d;var h=r(1719),x=r(1072),m=r(8602),u=r(8628),f=r(3722),j=r(4282),p=r(3204),g=r(4312),A=r(4117);const N=()=>{const{t:e}=(0,A.Bd)(),[s,r]=(0,a.useState)(!0),[l,t]=(0,a.useState)([]),[i,d]=(0,a.useState)([]),[N,_]=(0,a.useState)(new Set),[b,v]=(0,a.useState)(null),[y,w]=(0,a.useState)("");(0,a.useEffect)(()=>{(async()=>{const s=(0,g.b)();if(s){r(!0),v(null);try{const{data:{user:a}}=await s.auth.getUser();if(!a)return v(e("user_not_logged_in")),void r(!1);const{data:n,error:l}=await s.from("agent_profiles").select("maker_id").eq("user_id",a.id).single();if(l)return console.error("Error fetching agent profile:",l),v(e("agent_profile_not_found")),void r(!1);const{data:i,error:c}=await s.from("users").select("id, email, referred_by, created_at, role").order("created_at",{ascending:!0});if(c)return console.error("Error fetching users:",c),v(e("failed_to_load_referral_data")),void r(!1);const o=$(i);t(o),d(o)}catch(a){console.error("Error:",a),v(e("unexpected_error"))}finally{r(!1)}}})()},[e]),(0,a.useEffect)(()=>{if(!y.trim())return void d(l);const e=s=>s.filter(s=>{const r=s.email.toLowerCase().includes(y.toLowerCase())||s.id.toLowerCase().includes(y.toLowerCase()),a=s.children?e(s.children):[];return r||a.length>0}).map(s=>({...s,children:s.children?e(s.children):[]}));d(e(l))},[y,l]);const $=e=>{const s=new Map,r=[];return e.forEach(e=>{s.set(e.id,{...e,children:[]})}),e.forEach(e=>{if(e.referred_by){const a=s.get(e.referred_by);a?a.children.push(s.get(e.id)):r.push(s.get(e.id))}else r.push(s.get(e.id))}),r},C=e=>{switch(e){case"maker":return(0,c.jsx)(p.YXz,{className:"text-primary me-1"});case"agent":return(0,c.jsx)(p.x$1,{className:"text-success me-1"});case"customer":return(0,c.jsx)(p.x$1,{className:"text-info me-1"});default:return(0,c.jsx)(p.x$1,{className:"text-secondary me-1"})}},k=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;const r=e.children&&e.children.length>0,a=N.has(e.id),n=20*s;return(0,c.jsxs)("div",{className:"mb-1",children:[(0,c.jsxs)("div",{className:"d-flex align-items-center p-2 border-bottom",style:{paddingLeft:`${n}px`,cursor:r?"pointer":"default"},onClick:()=>r&&(e=>{const s=new Set(N);s.has(e)?s.delete(e):s.add(e),_(s)})(e.id),children:[r?a?(0,c.jsx)(p.Vr3,{className:"me-2 text-muted"}):(0,c.jsx)(p.X6T,{className:"me-2 text-muted"}):(0,c.jsx)("span",{className:"me-4"}),C(e.role),(0,c.jsx)("span",{className:"me-2",children:e.email}),(0,c.jsxs)("span",{className:"text-muted small",children:["[",(l=e.id,l.substring(0,8)),"]"]}),r&&(0,c.jsx)("span",{className:"ms-auto badge bg-secondary",children:e.children.length})]}),r&&a&&(0,c.jsx)("div",{children:e.children.map(e=>k(e,s+1))})]},e.id);var l};return s?(0,c.jsx)(n.A,{className:"d-flex justify-content-center align-items-center",style:{minHeight:"400px"},children:(0,c.jsxs)("div",{className:"text-center",children:[(0,c.jsx)(o,{animation:"border",role:"status",className:"mb-3"}),(0,c.jsx)("div",{children:e("loading_referral_data")})]})}):b?(0,c.jsx)(n.A,{children:(0,c.jsx)(h.A,{variant:"danger",children:b})}):(0,c.jsxs)(n.A,{children:[(0,c.jsx)(x.A,{className:"mb-4",children:(0,c.jsxs)(m.A,{children:[(0,c.jsx)("h2",{children:e("referral_relationships")}),(0,c.jsx)("p",{className:"text-muted",children:e("referral_tree_description")})]})}),(0,c.jsx)(x.A,{className:"mb-3",children:(0,c.jsx)(m.A,{children:(0,c.jsx)(u.A,{children:(0,c.jsx)(u.A.Body,{children:(0,c.jsxs)(x.A,{className:"align-items-end",children:[(0,c.jsx)(m.A,{md:4,children:(0,c.jsxs)(f.A.Group,{children:[(0,c.jsx)(f.A.Label,{children:e("search_users")}),(0,c.jsx)(f.A.Control,{type:"text",placeholder:e("search_by_email_or_id"),value:y,onChange:e=>w(e.target.value)})]})}),(0,c.jsx)(m.A,{md:4,children:(0,c.jsxs)("div",{className:"d-flex gap-2",children:[(0,c.jsxs)(j.A,{variant:"outline-primary",size:"sm",onClick:()=>{const e=new Set,s=r=>{r.forEach(r=>{r.children&&r.children.length>0&&(e.add(r.id),s(r.children))})};s(i),_(e)},children:[(0,c.jsx)(p.xKl,{className:"me-1"}),e("expand_all")]}),(0,c.jsxs)(j.A,{variant:"outline-secondary",size:"sm",onClick:()=>{_(new Set)},children:[(0,c.jsx)(p.f93,{className:"me-1"}),e("collapse_all")]})]})})]})})})})}),(0,c.jsxs)(x.A,{className:"mb-3",children:[(0,c.jsx)(m.A,{md:4,children:(0,c.jsx)(u.A,{className:"bg-primary text-white",children:(0,c.jsxs)(u.A.Body,{children:[(0,c.jsx)("h5",{children:e("total_users")}),(0,c.jsx)("h3",{children:(e=>{let s=0;const r=e=>{e.forEach(e=>{s++,e.children&&e.children.length>0&&r(e.children)})};return r(e),s})(i)})]})})}),(0,c.jsx)(m.A,{md:4,children:(0,c.jsx)(u.A,{className:"bg-success text-white",children:(0,c.jsxs)(u.A.Body,{children:[(0,c.jsx)("h5",{children:e("root_users")}),(0,c.jsx)("h3",{children:i.length})]})})}),(0,c.jsx)(m.A,{md:4,children:(0,c.jsx)(u.A,{className:"bg-info text-white",children:(0,c.jsxs)(u.A.Body,{children:[(0,c.jsx)("h5",{children:e("expanded_nodes")}),(0,c.jsx)("h3",{children:N.size})]})})})]}),(0,c.jsx)(x.A,{children:(0,c.jsx)(m.A,{children:(0,c.jsxs)(u.A,{children:[(0,c.jsxs)(u.A.Header,{children:[(0,c.jsx)("h5",{className:"mb-0",children:e("referral_tree")}),(0,c.jsx)("small",{className:"text-muted",children:e("click_to_expand_collapse")})]}),(0,c.jsx)(u.A.Body,{style:{maxHeight:"600px",overflowY:"auto"},children:0===i.length?(0,c.jsx)("div",{className:"text-center text-muted py-4",children:e(y?"no_search_results":"no_referral_data")}):(0,c.jsx)("div",{children:i.map(e=>k(e))})})]})})})]})}},1719:(e,s,r)=>{r.d(s,{A:()=>N});var a=r(8139),n=r.n(a),l=r(5043),t=r(1969),i=r(6618),c=r(7852),d=r(4488),o=r(579);const h=(0,d.A)("h4");h.displayName="DivStyledAsH4";const x=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l=h,...t}=e;return a=(0,c.oU)(a,"alert-heading"),(0,o.jsx)(l,{ref:s,className:n()(r,a),...t})});x.displayName="AlertHeading";const m=x;var u=r(7071);const f=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l=u.A,...t}=e;return a=(0,c.oU)(a,"alert-link"),(0,o.jsx)(l,{ref:s,className:n()(r,a),...t})});f.displayName="AlertLink";const j=f;var p=r(8072),g=r(5632);const A=l.forwardRef((e,s)=>{const{bsPrefix:r,show:a=!0,closeLabel:l="Close alert",closeVariant:d,className:h,children:x,variant:m="primary",onClose:u,dismissible:f,transition:j=p.A,...A}=(0,t.Zw)(e,{show:"onClose"}),N=(0,c.oU)(r,"alert"),_=(0,i.A)(e=>{u&&u(!1,e)}),b=!0===j?p.A:j,v=(0,o.jsxs)("div",{role:"alert",...b?void 0:A,ref:s,className:n()(h,N,m&&`${N}-${m}`,f&&`${N}-dismissible`),children:[f&&(0,o.jsx)(g.A,{onClick:_,"aria-label":l,variant:d}),x]});return b?(0,o.jsx)(b,{unmountOnExit:!0,...A,ref:void 0,in:a,children:v}):a?v:null});A.displayName="Alert";const N=Object.assign(A,{Link:j,Heading:m})}}]);
//# sourceMappingURL=462.c41bf72f.chunk.js.map