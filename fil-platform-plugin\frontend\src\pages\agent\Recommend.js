import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Spinner, <PERSON><PERSON>, But<PERSON>, Form } from 'react-bootstrap';
import { FaChevronDown, FaChevronRight, FaUser, FaUsers, FaExpandArrowsAlt, FaCompressArrowsAlt, FaSearch } from 'react-icons/fa';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const Recommend = () => {
    const { t } = useTranslation();
    const [loading, setLoading] = useState(true);
    const [referralTree, setReferralTree] = useState([]);
    const [filteredTree, setFilteredTree] = useState([]);
    const [expandedNodes, setExpandedNodes] = useState(new Set());
    const [error, setError] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');

    useEffect(() => {
        const fetchReferralData = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            setError(null);

            try {
                const { data: { user } } = await supabase.auth.getUser();

                if (!user) {
                    setError(t('user_not_logged_in'));
                    setLoading(false);
                    return;
                }

                // First, get the agent's maker_id to determine scope
                const { data: agentProfile, error: agentError } = await supabase
                    .from('agent_profiles')
                    .select('maker_id')
                    .eq('user_id', user.id)
                    .single();

                if (agentError) {
                    console.error('Error fetching agent profile:', agentError);
                    setError(t('agent_profile_not_found'));
                    setLoading(false);
                    return;
                }

                // For agent view, we'll fetch users related to this agent's customers
                // First get all customers under this agent (limit to prevent large queries)
                const { data: customers, error: customersError } = await supabase
                    .from('customer_profiles')
                    .select(`
                        user_id,
                        users (
                            id,
                            email,
                            referred_by,
                            created_at,
                            role
                        )
                    `)
                    .eq('agent_id', user.id)
                    .limit(100); // Limit to prevent large queries

                if (customersError) {
                    console.error('Error fetching customers:', customersError);
                    setError(t('failed_to_load_referral_data'));
                    setLoading(false);
                    return;
                }

                // Extract user data from customers
                const customerUsers = customers?.map(c => c.users).filter(Boolean) || [];

                // Get referrer information recursively (up to 3 levels to avoid infinite loops)
                const fetchReferrers = async (userIds, level = 0, maxLevel = 3) => {
                    if (level >= maxLevel || userIds.length === 0) {
                        return [];
                    }

                    try {
                        const { data: referrerData, error: referrerError } = await supabase
                            .from('users')
                            .select('id, email, referred_by, created_at, role')
                            .in('id', userIds)
                            .limit(50); // Limit each level to prevent large queries

                        if (referrerError) {
                            console.error(`Error fetching referrers at level ${level}:`, referrerError);
                            return [];
                        }

                        const referrers = referrerData || [];

                        // Get next level referrer IDs
                        const nextLevelIds = [...new Set(referrers
                            .map(u => u.referred_by)
                            .filter(Boolean))];

                        // Recursively fetch next level
                        const nextLevelReferrers = await fetchReferrers(nextLevelIds, level + 1, maxLevel);

                        return [...referrers, ...nextLevelReferrers];
                    } catch (error) {
                        console.error(`Error in fetchReferrers at level ${level}:`, error);
                        return [];
                    }
                };

                // Get unique referred_by IDs from customers
                const initialReferrerIds = [...new Set(customerUsers
                    .map(u => u.referred_by)
                    .filter(Boolean))];

                const referrers = await fetchReferrers(initialReferrerIds);

                // Combine customer users and their referrers
                const allUsers = [...customerUsers, ...referrers];

                // Remove duplicates based on user ID
                const uniqueUsers = allUsers.filter((user, index, self) =>
                    index === self.findIndex(u => u.id === user.id)
                );

                // Build the referral tree
                const tree = buildReferralTree(uniqueUsers);
                setReferralTree(tree);
                setFilteredTree(tree);

            } catch (err) {
                console.error('Error:', err);

                // Fallback: try to get just the direct customers without referrer data
                try {
                    console.log('Attempting fallback query...');
                    const { data: simpleCustomers, error: simpleError } = await supabase
                        .from('customer_profiles')
                        .select(`
                            users (
                                id,
                                email,
                                created_at,
                                role
                            )
                        `)
                        .eq('agent_id', user.id)
                        .limit(50); // Further limit for fallback

                    if (!simpleError && simpleCustomers && simpleCustomers.length > 0) {
                        const simpleUsers = simpleCustomers
                            .map(c => c.users)
                            .filter(Boolean)
                            .map(u => ({
                                ...u,
                                referred_by: null,
                                children: []
                            })); // Remove referral info for simplicity

                        console.log('Fallback successful, loaded', simpleUsers.length, 'users');
                        setReferralTree(simpleUsers);
                        setFilteredTree(simpleUsers);
                        setError(t('limited_referral_data'));
                    } else {
                        console.log('Fallback failed or no data:', simpleError);
                        setReferralTree([]);
                        setFilteredTree([]);
                        setError(t('no_referral_data'));
                    }
                } catch (fallbackErr) {
                    console.error('Fallback error:', fallbackErr);
                    setReferralTree([]);
                    setFilteredTree([]);
                    setError(t('unexpected_error'));
                }
            } finally {
                setLoading(false);
            }
        };

        fetchReferralData();
    }, [t]);

    // Filter tree based on search term
    useEffect(() => {
        if (!searchTerm.trim()) {
            setFilteredTree(referralTree);
            return;
        }

        const filterTree = (nodes) => {
            return nodes.filter(node => {
                const matchesSearch = node.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                    node.id.toLowerCase().includes(searchTerm.toLowerCase());

                const filteredChildren = node.children ? filterTree(node.children) : [];

                return matchesSearch || filteredChildren.length > 0;
            }).map(node => ({
                ...node,
                children: node.children ? filterTree(node.children) : []
            }));
        };

        setFilteredTree(filterTree(referralTree));
    }, [searchTerm, referralTree]);

    const buildReferralTree = (users) => {
        if (!users || users.length === 0) {
            return [];
        }

        const userMap = new Map();
        const rootUsers = [];

        // Create a map of all users
        users.forEach(user => {
            userMap.set(user.id, {
                ...user,
                children: []
            });
        });

        // Build the tree structure
        users.forEach(user => {
            if (user.referred_by) {
                const parent = userMap.get(user.referred_by);
                if (parent) {
                    parent.children.push(userMap.get(user.id));
                } else {
                    // Parent not found, treat as root
                    rootUsers.push(userMap.get(user.id));
                }
            } else {
                // No referrer, this is a root user
                rootUsers.push(userMap.get(user.id));
            }
        });

        return rootUsers;
    };

    const toggleNode = (userId) => {
        const newExpanded = new Set(expandedNodes);
        if (newExpanded.has(userId)) {
            newExpanded.delete(userId);
        } else {
            newExpanded.add(userId);
        }
        setExpandedNodes(newExpanded);
    };

    const expandAll = () => {
        const allNodeIds = new Set();
        const collectNodeIds = (nodes) => {
            nodes.forEach(node => {
                if (node.children && node.children.length > 0) {
                    allNodeIds.add(node.id);
                    collectNodeIds(node.children);
                }
            });
        };
        collectNodeIds(filteredTree);
        setExpandedNodes(allNodeIds);
    };

    const collapseAll = () => {
        setExpandedNodes(new Set());
    };

    const formatUserId = (id) => {
        return id.substring(0, 8);
    };

    const getRoleIcon = (role) => {
        switch (role) {
            case 'maker':
                return <FaUsers className="text-primary me-1" />;
            case 'agent':
                return <FaUser className="text-success me-1" />;
            case 'customer':
                return <FaUser className="text-info me-1" />;
            default:
                return <FaUser className="text-secondary me-1" />;
        }
    };

    const renderTreeNode = (node, level = 0) => {
        const hasChildren = node.children && node.children.length > 0;
        const isExpanded = expandedNodes.has(node.id);
        const paddingLeft = level * 20;

        return (
            <div key={node.id} className="mb-1">
                <div 
                    className="d-flex align-items-center p-2 border-bottom"
                    style={{ paddingLeft: `${paddingLeft}px`, cursor: hasChildren ? 'pointer' : 'default' }}
                    onClick={() => hasChildren && toggleNode(node.id)}
                >
                    {hasChildren ? (
                        isExpanded ? (
                            <FaChevronDown className="me-2 text-muted" />
                        ) : (
                            <FaChevronRight className="me-2 text-muted" />
                        )
                    ) : (
                        <span className="me-4"></span>
                    )}
                    
                    {getRoleIcon(node.role)}
                    
                    <span className="me-2">
                        {node.email}
                    </span>
                    
                    <span className="text-muted small">
                        [{formatUserId(node.id)}]
                    </span>
                    
                    {hasChildren && (
                        <span className="ms-auto badge bg-secondary">
                            {node.children.length}
                        </span>
                    )}
                </div>
                
                {hasChildren && isExpanded && (
                    <div>
                        {node.children.map(child => renderTreeNode(child, level + 1))}
                    </div>
                )}
            </div>
        );
    };

    const getTotalUsers = (nodes) => {
        let total = 0;
        const countNodes = (nodeList) => {
            nodeList.forEach(node => {
                total++;
                if (node.children && node.children.length > 0) {
                    countNodes(node.children);
                }
            });
        };
        countNodes(nodes);
        return total;
    };

    const getRootUsersCount = () => {
        return filteredTree.length;
    };

    if (loading) {
        return (
            <Container className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
                <div className="text-center">
                    <Spinner animation="border" role="status" className="mb-3" />
                    <div>{t('loading_referral_data')}</div>
                </div>
            </Container>
        );
    }

    if (error) {
        return (
            <Container>
                <Alert variant="danger">
                    {error}
                </Alert>
            </Container>
        );
    }

    return (
        <Container>
            <Row className="mb-4">
                <Col>
                    <h2>{t('referral_relationships')}</h2>
                    <p className="text-muted">{t('referral_tree_description')}</p>
                </Col>
            </Row>

            <Row className="mb-3">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={4}>
                                    <Form.Group>
                                        <Form.Label>{t('search_users')}</Form.Label>
                                        <Form.Control
                                            type="text"
                                            placeholder={t('search_by_email_or_id')}
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={4}>
                                    <div className="d-flex gap-2">
                                        <Button
                                            variant="outline-primary"
                                            size="sm"
                                            onClick={expandAll}
                                        >
                                            <FaExpandArrowsAlt className="me-1" />
                                            {t('expand_all')}
                                        </Button>
                                        <Button
                                            variant="outline-secondary"
                                            size="sm"
                                            onClick={collapseAll}
                                        >
                                            <FaCompressArrowsAlt className="me-1" />
                                            {t('collapse_all')}
                                        </Button>
                                    </div>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row className="mb-3">
                <Col md={4}>
                    <Card className="bg-primary text-white">
                        <Card.Body>
                            <h5>{t('total_users')}</h5>
                            <h3>{getTotalUsers(filteredTree)}</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={4}>
                    <Card className="bg-success text-white">
                        <Card.Body>
                            <h5>{t('root_users')}</h5>
                            <h3>{getRootUsersCount()}</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={4}>
                    <Card className="bg-info text-white">
                        <Card.Body>
                            <h5>{t('expanded_nodes')}</h5>
                            <h3>{expandedNodes.size}</h3>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">{t('referral_tree')}</h5>
                            <small className="text-muted">
                                {t('click_to_expand_collapse')}
                            </small>
                        </Card.Header>
                        <Card.Body style={{ maxHeight: '600px', overflowY: 'auto' }}>
                            {filteredTree.length === 0 ? (
                                <div className="text-center text-muted py-4">
                                    {searchTerm ? t('no_search_results') : t('no_referral_data')}
                                </div>
                            ) : (
                                <div>
                                    {filteredTree.map(node => renderTreeNode(node))}
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default Recommend;
