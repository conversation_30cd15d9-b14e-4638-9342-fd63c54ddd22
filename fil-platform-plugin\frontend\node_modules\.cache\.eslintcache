[{"D:\\New_System\\fil-platform-plugin\\frontend\\src\\index.js": "1", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\App.js": "2", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\i18n.js": "3", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\supabaseClient.js": "4", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\LoginPage.js": "5", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerOrderListPage.js": "6", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Dashboard.js": "7", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\RecommendPage.js": "8", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerProductListPage.js": "9", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\KycPage.js": "10", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyGainsPage.js": "11", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyAccountPage.js": "12", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\WalletPage.js": "13", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\OrderListPage.js": "14", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ProductListPage.js": "15", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Dashboard.js": "16", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Dashboard.js": "17", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentProductListPage.js": "18", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentMemberList.js": "19", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerMiners.js": "20", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerFacilities.js": "21", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerSnapshots.js": "22", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Transactions.js": "23", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerEarnings.js": "24", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderReports.js": "25", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderDistributions.js": "26", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CustomerAssets.js": "27", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\NetworkStats.js": "28", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CoinBatches.js": "29", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\ManualDeposits.js": "30", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CapacityRequest.js": "31", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\DebugAgent.js": "32", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Recommend.js": "33", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\NetworkStats.js": "34", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\CapacityRequest.js": "35", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Members.js": "36"}, {"size": 408, "mtime": 1751951658003, "results": "37", "hashOfConfig": "38"}, {"size": 17168, "mtime": 1752198601608, "results": "39", "hashOfConfig": "38"}, {"size": 47559, "mtime": 1752198769818, "results": "40", "hashOfConfig": "38"}, {"size": 1212, "mtime": 1751873051207, "results": "41", "hashOfConfig": "38"}, {"size": 3250, "mtime": 1751953679420, "results": "42", "hashOfConfig": "38"}, {"size": 4791, "mtime": 1751960448046, "results": "43", "hashOfConfig": "38"}, {"size": 3994, "mtime": 1752113564124, "results": "44", "hashOfConfig": "38"}, {"size": 4610, "mtime": 1751946228349, "results": "45", "hashOfConfig": "38"}, {"size": 5273, "mtime": 1751960463052, "results": "46", "hashOfConfig": "38"}, {"size": 8598, "mtime": 1751939997191, "results": "47", "hashOfConfig": "38"}, {"size": 4230, "mtime": 1751940026705, "results": "48", "hashOfConfig": "38"}, {"size": 1735, "mtime": 1751940008151, "results": "49", "hashOfConfig": "38"}, {"size": 4711, "mtime": 1752120569812, "results": "50", "hashOfConfig": "38"}, {"size": 4495, "mtime": 1751940037703, "results": "51", "hashOfConfig": "38"}, {"size": 3655, "mtime": 1751948557098, "results": "52", "hashOfConfig": "38"}, {"size": 4863, "mtime": 1751950041198, "results": "53", "hashOfConfig": "38"}, {"size": 9836, "mtime": 1752196570631, "results": "54", "hashOfConfig": "38"}, {"size": 4027, "mtime": 1751945104895, "results": "55", "hashOfConfig": "38"}, {"size": 3679, "mtime": 1751944188070, "results": "56", "hashOfConfig": "38"}, {"size": 13565, "mtime": 1752120431667, "results": "57", "hashOfConfig": "38"}, {"size": 10902, "mtime": 1752120494989, "results": "58", "hashOfConfig": "38"}, {"size": 4650, "mtime": 1752111918233, "results": "59", "hashOfConfig": "38"}, {"size": 5979, "mtime": 1752111934504, "results": "60", "hashOfConfig": "38"}, {"size": 4355, "mtime": 1752111904775, "results": "61", "hashOfConfig": "38"}, {"size": 6983, "mtime": 1752115841378, "results": "62", "hashOfConfig": "38"}, {"size": 7638, "mtime": 1752115874068, "results": "63", "hashOfConfig": "38"}, {"size": 4798, "mtime": 1752121974201, "results": "64", "hashOfConfig": "38"}, {"size": 3286, "mtime": 1752114312287, "results": "65", "hashOfConfig": "38"}, {"size": 6196, "mtime": 1752114298217, "results": "66", "hashOfConfig": "38"}, {"size": 7110, "mtime": 1752120864192, "results": "67", "hashOfConfig": "38"}, {"size": 7223, "mtime": 1752119937411, "results": "68", "hashOfConfig": "38"}, {"size": 7467, "mtime": 1752122455950, "results": "69", "hashOfConfig": "38"}, {"size": 13365, "mtime": 1752198732490, "results": "70", "hashOfConfig": "38"}, {"size": 3082, "mtime": 1752196922906, "results": "71", "hashOfConfig": "38"}, {"size": 7923, "mtime": 1752196766555, "results": "72", "hashOfConfig": "38"}, {"size": 15831, "mtime": 1752198022961, "results": "73", "hashOfConfig": "38"}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ji7irk", {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\New_System\\fil-platform-plugin\\frontend\\src\\index.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\App.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\i18n.js", ["182", "183", "184", "185", "186", "187", "188", "189", "190", "191", "192", "193", "194", "195", "196"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\supabaseClient.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\LoginPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerOrderListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\RecommendPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\KycPage.js", ["197"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyGainsPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyAccountPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\WalletPage.js", ["198"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\OrderListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Dashboard.js", ["199"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentMemberList.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerMiners.js", ["200"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerFacilities.js", ["201"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerSnapshots.js", ["202"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Transactions.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerEarnings.js", ["203"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderReports.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\OrderDistributions.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CustomerAssets.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\NetworkStats.js", ["204"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CoinBatches.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\ManualDeposits.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\CapacityRequest.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\DebugAgent.js", ["205", "206"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Recommend.js", ["207", "208"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\NetworkStats.js", ["209"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\CapacityRequest.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Members.js", ["210"], [], {"ruleId": "211", "severity": 1, "message": "212", "line": 180, "column": 7, "nodeType": "213", "messageId": "214", "endLine": 180, "endColumn": 18}, {"ruleId": "211", "severity": 1, "message": "215", "line": 182, "column": 7, "nodeType": "213", "messageId": "214", "endLine": 182, "endColumn": 23}, {"ruleId": "211", "severity": 1, "message": "216", "line": 293, "column": 7, "nodeType": "213", "messageId": "214", "endLine": 293, "endColumn": 17}, {"ruleId": "211", "severity": 1, "message": "217", "line": 340, "column": 7, "nodeType": "213", "messageId": "214", "endLine": 340, "endColumn": 26}, {"ruleId": "211", "severity": 1, "message": "218", "line": 341, "column": 7, "nodeType": "213", "messageId": "214", "endLine": 341, "endColumn": 27}, {"ruleId": "211", "severity": 1, "message": "212", "line": 528, "column": 7, "nodeType": "213", "messageId": "214", "endLine": 528, "endColumn": 18}, {"ruleId": "211", "severity": 1, "message": "215", "line": 530, "column": 7, "nodeType": "213", "messageId": "214", "endLine": 530, "endColumn": 23}, {"ruleId": "211", "severity": 1, "message": "216", "line": 641, "column": 7, "nodeType": "213", "messageId": "214", "endLine": 641, "endColumn": 17}, {"ruleId": "211", "severity": 1, "message": "217", "line": 688, "column": 7, "nodeType": "213", "messageId": "214", "endLine": 688, "endColumn": 26}, {"ruleId": "211", "severity": 1, "message": "218", "line": 689, "column": 7, "nodeType": "213", "messageId": "214", "endLine": 689, "endColumn": 27}, {"ruleId": "211", "severity": 1, "message": "212", "line": 876, "column": 7, "nodeType": "213", "messageId": "214", "endLine": 876, "endColumn": 18}, {"ruleId": "211", "severity": 1, "message": "215", "line": 878, "column": 7, "nodeType": "213", "messageId": "214", "endLine": 878, "endColumn": 23}, {"ruleId": "211", "severity": 1, "message": "216", "line": 989, "column": 7, "nodeType": "213", "messageId": "214", "endLine": 989, "endColumn": 17}, {"ruleId": "211", "severity": 1, "message": "217", "line": 1036, "column": 7, "nodeType": "213", "messageId": "214", "endLine": 1036, "endColumn": 26}, {"ruleId": "211", "severity": 1, "message": "218", "line": 1037, "column": 7, "nodeType": "213", "messageId": "214", "endLine": 1037, "endColumn": 27}, {"ruleId": "219", "severity": 1, "message": "220", "line": 49, "column": 8, "nodeType": "221", "endLine": 49, "endColumn": 10, "suggestions": "222"}, {"ruleId": "223", "severity": 1, "message": "224", "line": 3, "column": 44, "nodeType": "225", "messageId": "226", "endLine": 3, "endColumn": 50}, {"ruleId": "223", "severity": 1, "message": "227", "line": 23, "column": 46, "nodeType": "225", "messageId": "226", "endLine": 23, "endColumn": 57}, {"ruleId": "223", "severity": 1, "message": "228", "line": 2, "column": 44, "nodeType": "225", "messageId": "226", "endLine": 2, "endColumn": 49}, {"ruleId": "223", "severity": 1, "message": "228", "line": 2, "column": 44, "nodeType": "225", "messageId": "226", "endLine": 2, "endColumn": 49}, {"ruleId": "223", "severity": 1, "message": "228", "line": 2, "column": 44, "nodeType": "225", "messageId": "226", "endLine": 2, "endColumn": 49}, {"ruleId": "223", "severity": 1, "message": "228", "line": 2, "column": 44, "nodeType": "225", "messageId": "226", "endLine": 2, "endColumn": 49}, {"ruleId": "223", "severity": 1, "message": "228", "line": 2, "column": 44, "nodeType": "225", "messageId": "226", "endLine": 2, "endColumn": 49}, {"ruleId": "223", "severity": 1, "message": "229", "line": 7, "column": 13, "nodeType": "225", "messageId": "226", "endLine": 7, "endColumn": 14}, {"ruleId": "223", "severity": 1, "message": "230", "line": 90, "column": 21, "nodeType": "225", "messageId": "226", "endLine": 90, "endColumn": 25}, {"ruleId": "223", "severity": 1, "message": "231", "line": 3, "column": 98, "nodeType": "225", "messageId": "226", "endLine": 3, "endColumn": 106}, {"ruleId": "223", "severity": 1, "message": "232", "line": 34, "column": 31, "nodeType": "225", "messageId": "226", "endLine": 34, "endColumn": 43}, {"ruleId": "223", "severity": 1, "message": "228", "line": 2, "column": 44, "nodeType": "225", "messageId": "226", "endLine": 2, "endColumn": 49}, {"ruleId": "223", "severity": 1, "message": "233", "line": 2, "column": 77, "nodeType": "225", "messageId": "226", "endLine": 2, "endColumn": 85}, "no-dupe-keys", "Duplicate key 'no_assets'.", "ObjectExpression", "unexpected", "Duplicate key 'my_invite_code'.", "Duplicate key 'batch_id'.", "Duplicate key 'member_management'.", "Duplicate key 'product_management'.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 't'. Either include it or remove the dependency array.", "ArrayExpression", ["234"], "no-unused-vars", "'Button' is defined but never used.", "Identifier", "unusedVar", "'memberError' is assigned a value but never used.", "'Badge' is defined but never used.", "'t' is assigned a value but never used.", "'data' is assigned a value but never used.", "'FaSearch' is defined but never used.", "'agentProfile' is assigned a value but never used.", "'Dropdown' is defined but never used.", {"desc": "235", "fix": "236"}, "Update the dependencies array to be: [t]", {"range": "237", "text": "238"}, [1977, 1979], "[t]"]