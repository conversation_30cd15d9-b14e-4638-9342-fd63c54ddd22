{"version": 3, "file": "static/js/462.c41bf72f.chunk.js", "mappings": "wKAMA,MAAMA,EAAmBC,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,OACjDO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIE,SADGZ,EAAMU,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GAChD,MAARE,GAAcJ,EAAQM,KAAK,GAAGP,IAAaM,KAASD,QAEtCG,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWI,KAAsBO,OAG3DhB,EAAIyB,YAAc,MAClB,S,6GCjCA,MAAMC,EAAuBzB,EAAAA,WAAiB,CAAAC,EAS3CC,KAAQ,IAToC,SAC7CC,EAAQ,QACRuB,EAAO,UACPC,EAAY,SAAQ,KACpBC,EAEAvB,GAAIC,EAAY,MAAK,UACrBF,KACGG,GACJN,EACCE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,WACxC,MAAM0B,EAAkB,GAAG1B,KAAYwB,IACvC,OAAoBL,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWyB,EAAiBD,GAAQ,GAAGC,KAAmBD,IAAQF,GAAW,QAAQA,SAG/GD,EAAQD,YAAc,UACtB,U,8FCnBA,MAyWA,EAzWkBM,KACd,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAcC,IAAmBF,EAAAA,EAAAA,UAAS,KAC1CG,EAAcC,IAAmBJ,EAAAA,EAAAA,UAAS,KAC1CK,EAAeC,IAAoBN,EAAAA,EAAAA,UAAS,IAAIO,MAChDC,EAAOC,IAAYT,EAAAA,EAAAA,UAAS,OAC5BU,EAAYC,IAAiBX,EAAAA,EAAAA,UAAS,KAE7CY,EAAAA,EAAAA,WAAU,KACoBC,WACtB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,GAAKD,EAAL,CAEAf,GAAW,GACXU,EAAS,MAET,IACI,MAAQO,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAE/C,IAAKF,EAGD,OAFAR,EAASb,EAAE,4BACXG,GAAW,GAKf,MAAQiB,KAAMI,EAAcZ,MAAOa,SAAqBP,EACnDQ,KAAK,kBACLC,OAAO,YACPC,GAAG,UAAWP,EAAKQ,IACnBC,SAEL,GAAIL,EAIA,OAHAM,QAAQnB,MAAM,gCAAiCa,GAC/CZ,EAASb,EAAE,iCACXG,GAAW,GAKf,MAAQiB,KAAMY,EAAUpB,MAAOqB,SAAqBf,EAC/CQ,KAAK,SACLC,OAAO,4CACPO,MAAM,aAAc,CAAEC,WAAW,IAEtC,GAAIF,EAIA,OAHAF,QAAQnB,MAAM,wBAAyBqB,GACvCpB,EAASb,EAAE,sCACXG,GAAW,GAKf,MAAMiC,EAAOC,EAAkBL,GAC/B1B,EAAgB8B,GAChB5B,EAAgB4B,EAEpB,CAAE,MAAOE,GACLP,QAAQnB,MAAM,SAAU0B,GACxBzB,EAASb,EAAE,oBACf,CAAC,QACGG,GAAW,EACf,CAnDqB,GAsDzBoC,IACD,CAACvC,KAGJgB,EAAAA,EAAAA,WAAU,KACN,IAAKF,EAAW0B,OAEZ,YADAhC,EAAgBH,GAIpB,MAAMoC,EAAcC,GACTA,EAAMC,OAAOC,IAChB,MAAMC,EAAgBD,EAAKE,MAAMC,cAAcC,SAASlC,EAAWiC,gBAC/CH,EAAKf,GAAGkB,cAAcC,SAASlC,EAAWiC,eAExDE,EAAmBL,EAAKM,SAAWT,EAAWG,EAAKM,UAAY,GAErE,OAAOL,GAAiBI,EAAiBE,OAAS,IACnDC,IAAIR,IAAI,IACJA,EACHM,SAAUN,EAAKM,SAAWT,EAAWG,EAAKM,UAAY,MAI9D1C,EAAgBiC,EAAWpC,KAC5B,CAACS,EAAYT,IAEhB,MAAMgC,EAAqBgB,IACvB,MAAMC,EAAU,IAAIC,IACdC,EAAY,GA0BlB,OAvBAH,EAAMpE,QAAQoC,IACViC,EAAQG,IAAIpC,EAAKQ,GAAI,IACdR,EACH6B,SAAU,OAKlBG,EAAMpE,QAAQoC,IACV,GAAIA,EAAKqC,YAAa,CAClB,MAAMC,EAASL,EAAQM,IAAIvC,EAAKqC,aAC5BC,EACAA,EAAOT,SAAS5D,KAAKgE,EAAQM,IAAIvC,EAAKQ,KAGtC2B,EAAUlE,KAAKgE,EAAQM,IAAIvC,EAAKQ,IAExC,MAEI2B,EAAUlE,KAAKgE,EAAQM,IAAIvC,EAAKQ,OAIjC2B,GAmCLK,EAAeC,IACjB,OAAQA,GACJ,IAAK,QACD,OAAOvE,EAAAA,EAAAA,KAACwE,EAAAA,IAAO,CAAC1F,UAAU,sBAC9B,IAAK,QACD,OAAOkB,EAAAA,EAAAA,KAACyE,EAAAA,IAAM,CAAC3F,UAAU,sBAC7B,IAAK,WACD,OAAOkB,EAAAA,EAAAA,KAACyE,EAAAA,IAAM,CAAC3F,UAAU,mBAC7B,QACI,OAAOkB,EAAAA,EAAAA,KAACyE,EAAAA,IAAM,CAAC3F,UAAU,0BAI/B4F,EAAiB,SAACrB,GAAqB,IAAfsB,EAAKC,UAAAhB,OAAA,QAAAiB,IAAAD,UAAA,GAAAA,UAAA,GAAG,EAClC,MAAME,EAAczB,EAAKM,UAAYN,EAAKM,SAASC,OAAS,EACtDmB,EAAa7D,EAAc8D,IAAI3B,EAAKf,IACpC2C,EAAsB,GAARN,EAEpB,OACIO,EAAAA,EAAAA,MAAA,OAAmBpG,UAAU,OAAM6E,SAAA,EAC/BuB,EAAAA,EAAAA,MAAA,OACIpG,UAAU,8CACVqG,MAAO,CAAEF,YAAa,GAAGA,MAAiBG,OAAQN,EAAc,UAAY,WAC5EO,QAASA,IAAMP,GAvDXQ,KAChB,MAAMC,EAAc,IAAInE,IAAIF,GACxBqE,EAAYP,IAAIM,GAChBC,EAAYC,OAAOF,GAEnBC,EAAYE,IAAIH,GAEpBnE,EAAiBoE,IAgDyBG,CAAWrC,EAAKf,IAAIqB,SAAA,CAEjDmB,EACGC,GACI/E,EAAAA,EAAAA,KAAC2F,EAAAA,IAAa,CAAC7G,UAAU,qBAEzBkB,EAAAA,EAAAA,KAAC4F,EAAAA,IAAc,CAAC9G,UAAU,qBAG9BkB,EAAAA,EAAAA,KAAA,QAAMlB,UAAU,SAGnBwF,EAAYjB,EAAKkB,OAElBvE,EAAAA,EAAAA,KAAA,QAAMlB,UAAU,OAAM6E,SACjBN,EAAKE,SAGV2B,EAAAA,EAAAA,MAAA,QAAMpG,UAAU,mBAAkB6E,SAAA,CAAC,KA7C7BrB,EA8Cae,EAAKf,GA7C7BA,EAAGuD,UAAU,EAAG,IA6CiB,OAG3Bf,IACG9E,EAAAA,EAAAA,KAAA,QAAMlB,UAAU,6BAA4B6E,SACvCN,EAAKM,SAASC,YAK1BkB,GAAeC,IACZ/E,EAAAA,EAAAA,KAAA,OAAA2D,SACKN,EAAKM,SAASE,IAAIiC,GAASpB,EAAeoB,EAAOnB,EAAQ,QAnC5DtB,EAAKf,IAvBDA,KA+DtB,EAoBA,OAAI3B,GAEIX,EAAAA,EAAAA,KAAC+F,EAAAA,EAAS,CAACjH,UAAU,mDAAmDqG,MAAO,CAAEa,UAAW,SAAUrC,UAClGuB,EAAAA,EAAAA,MAAA,OAAKpG,UAAU,cAAa6E,SAAA,EACxB3D,EAAAA,EAAAA,KAACG,EAAO,CAACE,UAAU,SAASkE,KAAK,SAASzF,UAAU,UACpDkB,EAAAA,EAAAA,KAAA,OAAA2D,SAAMlD,EAAE,gCAMpBY,GAEIrB,EAAAA,EAAAA,KAAC+F,EAAAA,EAAS,CAAApC,UACN3D,EAAAA,EAAAA,KAACiG,EAAAA,EAAK,CAAC7F,QAAQ,SAAQuD,SAClBtC,OAOb6D,EAAAA,EAAAA,MAACa,EAAAA,EAAS,CAAApC,SAAA,EACN3D,EAAAA,EAAAA,KAACvB,EAAAA,EAAG,CAACK,UAAU,OAAM6E,UACjBuB,EAAAA,EAAAA,MAACgB,EAAAA,EAAG,CAAAvC,SAAA,EACA3D,EAAAA,EAAAA,KAAA,MAAA2D,SAAKlD,EAAE,6BACPT,EAAAA,EAAAA,KAAA,KAAGlB,UAAU,aAAY6E,SAAElD,EAAE,qCAIrCT,EAAAA,EAAAA,KAACvB,EAAAA,EAAG,CAACK,UAAU,OAAM6E,UACjB3D,EAAAA,EAAAA,KAACkG,EAAAA,EAAG,CAAAvC,UACA3D,EAAAA,EAAAA,KAACmG,EAAAA,EAAI,CAAAxC,UACD3D,EAAAA,EAAAA,KAACmG,EAAAA,EAAKC,KAAI,CAAAzC,UACNuB,EAAAA,EAAAA,MAACzG,EAAAA,EAAG,CAACK,UAAU,kBAAiB6E,SAAA,EAC5B3D,EAAAA,EAAAA,KAACkG,EAAAA,EAAG,CAACG,GAAI,EAAE1C,UACPuB,EAAAA,EAAAA,MAACoB,EAAAA,EAAKC,MAAK,CAAA5C,SAAA,EACP3D,EAAAA,EAAAA,KAACsG,EAAAA,EAAKE,MAAK,CAAA7C,SAAElD,EAAE,mBACfT,EAAAA,EAAAA,KAACsG,EAAAA,EAAKG,QAAO,CACTC,KAAK,OACLC,YAAalG,EAAE,yBACfmG,MAAOrF,EACPsF,SAAWC,GAAMtF,EAAcsF,EAAEC,OAAOH,eAIpD5G,EAAAA,EAAAA,KAACkG,EAAAA,EAAG,CAACG,GAAI,EAAE1C,UACPuB,EAAAA,EAAAA,MAAA,OAAKpG,UAAU,eAAc6E,SAAA,EACzBuB,EAAAA,EAAAA,MAAC8B,EAAAA,EAAM,CACH5G,QAAQ,kBACRE,KAAK,KACL+E,QAxJtB4B,KACd,MAAMC,EAAa,IAAI9F,IACjB+F,EAAkBhE,IACpBA,EAAMzD,QAAQ2D,IACNA,EAAKM,UAAYN,EAAKM,SAASC,OAAS,IACxCsD,EAAWzB,IAAIpC,EAAKf,IACpB6E,EAAe9D,EAAKM,cAIhCwD,EAAenG,GACfG,EAAiB+F,IA6IsCvD,SAAA,EAEnB3D,EAAAA,EAAAA,KAACoH,EAAAA,IAAiB,CAACtI,UAAU,SAC5B2B,EAAE,kBAEPyE,EAAAA,EAAAA,MAAC8B,EAAAA,EAAM,CACH5G,QAAQ,oBACRE,KAAK,KACL+E,QAlJpBgC,KAChBlG,EAAiB,IAAIC,MAiJoCuC,SAAA,EAErB3D,EAAAA,EAAAA,KAACsH,EAAAA,IAAmB,CAACxI,UAAU,SAC9B2B,EAAE,oCAUnCyE,EAAAA,EAAAA,MAACzG,EAAAA,EAAG,CAACK,UAAU,OAAM6E,SAAA,EACjB3D,EAAAA,EAAAA,KAACkG,EAAAA,EAAG,CAACG,GAAI,EAAE1C,UACP3D,EAAAA,EAAAA,KAACmG,EAAAA,EAAI,CAACrH,UAAU,wBAAuB6E,UACnCuB,EAAAA,EAAAA,MAACiB,EAAAA,EAAKC,KAAI,CAAAzC,SAAA,EACN3D,EAAAA,EAAAA,KAAA,MAAA2D,SAAKlD,EAAE,kBACPT,EAAAA,EAAAA,KAAA,MAAA2D,SA/FDR,KACnB,IAAIoE,EAAQ,EACZ,MAAMC,EAAcC,IAChBA,EAAS/H,QAAQ2D,IACbkE,IACIlE,EAAKM,UAAYN,EAAKM,SAASC,OAAS,GACxC4D,EAAWnE,EAAKM,aAK5B,OADA6D,EAAWrE,GACJoE,GAoFkBG,CAAc1G,aAI/BhB,EAAAA,EAAAA,KAACkG,EAAAA,EAAG,CAACG,GAAI,EAAE1C,UACP3D,EAAAA,EAAAA,KAACmG,EAAAA,EAAI,CAACrH,UAAU,wBAAuB6E,UACnCuB,EAAAA,EAAAA,MAACiB,EAAAA,EAAKC,KAAI,CAAAzC,SAAA,EACN3D,EAAAA,EAAAA,KAAA,MAAA2D,SAAKlD,EAAE,iBACPT,EAAAA,EAAAA,KAAA,MAAA2D,SAxFb3C,EAAa4C,iBA4FZ5D,EAAAA,EAAAA,KAACkG,EAAAA,EAAG,CAACG,GAAI,EAAE1C,UACP3D,EAAAA,EAAAA,KAACmG,EAAAA,EAAI,CAACrH,UAAU,qBAAoB6E,UAChCuB,EAAAA,EAAAA,MAACiB,EAAAA,EAAKC,KAAI,CAAAzC,SAAA,EACN3D,EAAAA,EAAAA,KAAA,MAAA2D,SAAKlD,EAAE,qBACPT,EAAAA,EAAAA,KAAA,MAAA2D,SAAKzC,EAAcZ,kBAMnCN,EAAAA,EAAAA,KAACvB,EAAAA,EAAG,CAAAkF,UACA3D,EAAAA,EAAAA,KAACkG,EAAAA,EAAG,CAAAvC,UACAuB,EAAAA,EAAAA,MAACiB,EAAAA,EAAI,CAAAxC,SAAA,EACDuB,EAAAA,EAAAA,MAACiB,EAAAA,EAAKwB,OAAM,CAAAhE,SAAA,EACR3D,EAAAA,EAAAA,KAAA,MAAIlB,UAAU,OAAM6E,SAAElD,EAAE,oBACxBT,EAAAA,EAAAA,KAAA,SAAOlB,UAAU,aAAY6E,SACxBlD,EAAE,kCAGXT,EAAAA,EAAAA,KAACmG,EAAAA,EAAKC,KAAI,CAACjB,MAAO,CAAEyC,UAAW,QAASC,UAAW,QAASlE,SAC/B,IAAxB3C,EAAa4C,QACV5D,EAAAA,EAAAA,KAAA,OAAKlB,UAAU,8BAA6B6E,SAC1BlD,EAAbc,EAAe,oBAAyB,uBAG7CvB,EAAAA,EAAAA,KAAA,OAAA2D,SACK3C,EAAa6C,IAAIR,GAAQqB,EAAerB,kB,oHC7V7E,MAAMyE,GAAgBC,EAAAA,EAAAA,GAAiB,MACvCD,EAAc5H,YAAc,gBAC5B,MAAM8H,EAA4BtJ,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDE,EAAS,SACTD,EACAE,GAAIC,EAAY8I,KACb7I,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,kBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP+I,EAAa9H,YAAc,eAC3B,U,cChBA,MAAM+H,EAAyBvJ,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/CE,EAAS,SACTD,EACAE,GAAIC,EAAYkJ,EAAAA,KACbjJ,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,eACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPgJ,EAAU/H,YAAc,YACxB,U,wBCRA,MAAM+F,EAAqBvH,EAAAA,WAAiB,CAACyJ,EAAmBvJ,KAC9D,MAAM,SACJC,EAAQ,KACRuJ,GAAO,EAAI,WACXC,EAAa,cAAa,aAC1BC,EAAY,UACZxJ,EAAS,SACT6E,EAAQ,QACRvD,EAAU,UAAS,QACnBmI,EAAO,YACPC,EAAW,WACXC,EAAaC,EAAAA,KACVzJ,IACD0J,EAAAA,EAAAA,IAAgBR,EAAmB,CACrCC,KAAM,YAEFQ,GAASzJ,EAAAA,EAAAA,IAAmBN,EAAU,SACtCgK,GAAcC,EAAAA,EAAAA,GAAiBhC,IAC/ByB,GACFA,GAAQ,EAAOzB,KAGbiC,GAA4B,IAAfN,EAAsBC,EAAAA,EAAOD,EAC1CO,GAAqB9D,EAAAA,EAAAA,MAAM,MAAO,CACtCX,KAAM,WACDwE,OAAqBlE,EAAR5F,EAClBL,IAAKA,EACLE,UAAWmB,IAAWnB,EAAW8J,EAAQxI,GAAW,GAAGwI,KAAUxI,IAAWoI,GAAe,GAAGI,iBAC9FjF,SAAU,CAAC6E,IAA4BxI,EAAAA,EAAAA,KAAKiJ,EAAAA,EAAa,CACvD5D,QAASwD,EACT,aAAcR,EACdjI,QAASkI,IACP3E,KAEN,OAAKoF,GACe/I,EAAAA,EAAAA,KAAK+I,EAAY,CACnCG,eAAe,KACZjK,EACHL,SAAKiG,EACLsE,GAAIf,EACJzE,SAAUqF,IANYZ,EAAOY,EAAQ,OASzC/C,EAAM/F,YAAc,QACpB,QAAekJ,OAAOC,OAAOpD,EAAO,CAClCqD,KAAMrB,EACNsB,QAASvB,G", "sources": ["../node_modules/react-bootstrap/esm/Row.js", "../node_modules/react-bootstrap/esm/Spinner.js", "pages/agent/Recommend.js", "../node_modules/react-bootstrap/esm/AlertHeading.js", "../node_modules/react-bootstrap/esm/AlertLink.js", "../node_modules/react-bootstrap/esm/Alert.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Spinner = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  variant,\n  animation = 'border',\n  size,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  className,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'spinner');\n  const bsSpinnerPrefix = `${bsPrefix}-${animation}`;\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, bsSpinnerPrefix, size && `${bsSpinnerPrefix}-${size}`, variant && `text-${variant}`)\n  });\n});\nSpinner.displayName = 'Spinner';\nexport default Spinner;", "import React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Spinner, <PERSON><PERSON>, But<PERSON>, Form } from 'react-bootstrap';\nimport { FaChevronDown, FaChevronRight, FaUser, FaUsers, FaExpandArrowsAlt, FaCompressArrowsAlt, FaSearch } from 'react-icons/fa';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst Recommend = () => {\n    const { t } = useTranslation();\n    const [loading, setLoading] = useState(true);\n    const [referralTree, setReferralTree] = useState([]);\n    const [filteredTree, setFilteredTree] = useState([]);\n    const [expandedNodes, setExpandedNodes] = useState(new Set());\n    const [error, setError] = useState(null);\n    const [searchTerm, setSearchTerm] = useState('');\n\n    useEffect(() => {\n        const fetchReferralData = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            setError(null);\n\n            try {\n                const { data: { user } } = await supabase.auth.getUser();\n\n                if (!user) {\n                    setError(t('user_not_logged_in'));\n                    setLoading(false);\n                    return;\n                }\n\n                // First, get the agent's maker_id to determine scope\n                const { data: agentProfile, error: agentError } = await supabase\n                    .from('agent_profiles')\n                    .select('maker_id')\n                    .eq('user_id', user.id)\n                    .single();\n\n                if (agentError) {\n                    console.error('Error fetching agent profile:', agentError);\n                    setError(t('agent_profile_not_found'));\n                    setLoading(false);\n                    return;\n                }\n\n                // Fetch all users to build the referral tree\n                const { data: allUsers, error: usersError } = await supabase\n                    .from('users')\n                    .select('id, email, referred_by, created_at, role')\n                    .order('created_at', { ascending: true });\n\n                if (usersError) {\n                    console.error('Error fetching users:', usersError);\n                    setError(t('failed_to_load_referral_data'));\n                    setLoading(false);\n                    return;\n                }\n\n                // Build the referral tree\n                const tree = buildReferralTree(allUsers);\n                setReferralTree(tree);\n                setFilteredTree(tree);\n\n            } catch (err) {\n                console.error('Error:', err);\n                setError(t('unexpected_error'));\n            } finally {\n                setLoading(false);\n            }\n        };\n\n        fetchReferralData();\n    }, [t]);\n\n    // Filter tree based on search term\n    useEffect(() => {\n        if (!searchTerm.trim()) {\n            setFilteredTree(referralTree);\n            return;\n        }\n\n        const filterTree = (nodes) => {\n            return nodes.filter(node => {\n                const matchesSearch = node.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                                    node.id.toLowerCase().includes(searchTerm.toLowerCase());\n\n                const filteredChildren = node.children ? filterTree(node.children) : [];\n\n                return matchesSearch || filteredChildren.length > 0;\n            }).map(node => ({\n                ...node,\n                children: node.children ? filterTree(node.children) : []\n            }));\n        };\n\n        setFilteredTree(filterTree(referralTree));\n    }, [searchTerm, referralTree]);\n\n    const buildReferralTree = (users) => {\n        const userMap = new Map();\n        const rootUsers = [];\n\n        // Create a map of all users\n        users.forEach(user => {\n            userMap.set(user.id, {\n                ...user,\n                children: []\n            });\n        });\n\n        // Build the tree structure\n        users.forEach(user => {\n            if (user.referred_by) {\n                const parent = userMap.get(user.referred_by);\n                if (parent) {\n                    parent.children.push(userMap.get(user.id));\n                } else {\n                    // Parent not found, treat as root\n                    rootUsers.push(userMap.get(user.id));\n                }\n            } else {\n                // No referrer, this is a root user\n                rootUsers.push(userMap.get(user.id));\n            }\n        });\n\n        return rootUsers;\n    };\n\n    const toggleNode = (userId) => {\n        const newExpanded = new Set(expandedNodes);\n        if (newExpanded.has(userId)) {\n            newExpanded.delete(userId);\n        } else {\n            newExpanded.add(userId);\n        }\n        setExpandedNodes(newExpanded);\n    };\n\n    const expandAll = () => {\n        const allNodeIds = new Set();\n        const collectNodeIds = (nodes) => {\n            nodes.forEach(node => {\n                if (node.children && node.children.length > 0) {\n                    allNodeIds.add(node.id);\n                    collectNodeIds(node.children);\n                }\n            });\n        };\n        collectNodeIds(filteredTree);\n        setExpandedNodes(allNodeIds);\n    };\n\n    const collapseAll = () => {\n        setExpandedNodes(new Set());\n    };\n\n    const formatUserId = (id) => {\n        return id.substring(0, 8);\n    };\n\n    const getRoleIcon = (role) => {\n        switch (role) {\n            case 'maker':\n                return <FaUsers className=\"text-primary me-1\" />;\n            case 'agent':\n                return <FaUser className=\"text-success me-1\" />;\n            case 'customer':\n                return <FaUser className=\"text-info me-1\" />;\n            default:\n                return <FaUser className=\"text-secondary me-1\" />;\n        }\n    };\n\n    const renderTreeNode = (node, level = 0) => {\n        const hasChildren = node.children && node.children.length > 0;\n        const isExpanded = expandedNodes.has(node.id);\n        const paddingLeft = level * 20;\n\n        return (\n            <div key={node.id} className=\"mb-1\">\n                <div \n                    className=\"d-flex align-items-center p-2 border-bottom\"\n                    style={{ paddingLeft: `${paddingLeft}px`, cursor: hasChildren ? 'pointer' : 'default' }}\n                    onClick={() => hasChildren && toggleNode(node.id)}\n                >\n                    {hasChildren ? (\n                        isExpanded ? (\n                            <FaChevronDown className=\"me-2 text-muted\" />\n                        ) : (\n                            <FaChevronRight className=\"me-2 text-muted\" />\n                        )\n                    ) : (\n                        <span className=\"me-4\"></span>\n                    )}\n                    \n                    {getRoleIcon(node.role)}\n                    \n                    <span className=\"me-2\">\n                        {node.email}\n                    </span>\n                    \n                    <span className=\"text-muted small\">\n                        [{formatUserId(node.id)}]\n                    </span>\n                    \n                    {hasChildren && (\n                        <span className=\"ms-auto badge bg-secondary\">\n                            {node.children.length}\n                        </span>\n                    )}\n                </div>\n                \n                {hasChildren && isExpanded && (\n                    <div>\n                        {node.children.map(child => renderTreeNode(child, level + 1))}\n                    </div>\n                )}\n            </div>\n        );\n    };\n\n    const getTotalUsers = (nodes) => {\n        let total = 0;\n        const countNodes = (nodeList) => {\n            nodeList.forEach(node => {\n                total++;\n                if (node.children && node.children.length > 0) {\n                    countNodes(node.children);\n                }\n            });\n        };\n        countNodes(nodes);\n        return total;\n    };\n\n    const getRootUsersCount = () => {\n        return filteredTree.length;\n    };\n\n    if (loading) {\n        return (\n            <Container className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '400px' }}>\n                <div className=\"text-center\">\n                    <Spinner animation=\"border\" role=\"status\" className=\"mb-3\" />\n                    <div>{t('loading_referral_data')}</div>\n                </div>\n            </Container>\n        );\n    }\n\n    if (error) {\n        return (\n            <Container>\n                <Alert variant=\"danger\">\n                    {error}\n                </Alert>\n            </Container>\n        );\n    }\n\n    return (\n        <Container>\n            <Row className=\"mb-4\">\n                <Col>\n                    <h2>{t('referral_relationships')}</h2>\n                    <p className=\"text-muted\">{t('referral_tree_description')}</p>\n                </Col>\n            </Row>\n\n            <Row className=\"mb-3\">\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Row className=\"align-items-end\">\n                                <Col md={4}>\n                                    <Form.Group>\n                                        <Form.Label>{t('search_users')}</Form.Label>\n                                        <Form.Control\n                                            type=\"text\"\n                                            placeholder={t('search_by_email_or_id')}\n                                            value={searchTerm}\n                                            onChange={(e) => setSearchTerm(e.target.value)}\n                                        />\n                                    </Form.Group>\n                                </Col>\n                                <Col md={4}>\n                                    <div className=\"d-flex gap-2\">\n                                        <Button\n                                            variant=\"outline-primary\"\n                                            size=\"sm\"\n                                            onClick={expandAll}\n                                        >\n                                            <FaExpandArrowsAlt className=\"me-1\" />\n                                            {t('expand_all')}\n                                        </Button>\n                                        <Button\n                                            variant=\"outline-secondary\"\n                                            size=\"sm\"\n                                            onClick={collapseAll}\n                                        >\n                                            <FaCompressArrowsAlt className=\"me-1\" />\n                                            {t('collapse_all')}\n                                        </Button>\n                                    </div>\n                                </Col>\n                            </Row>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            <Row className=\"mb-3\">\n                <Col md={4}>\n                    <Card className=\"bg-primary text-white\">\n                        <Card.Body>\n                            <h5>{t('total_users')}</h5>\n                            <h3>{getTotalUsers(filteredTree)}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"bg-success text-white\">\n                        <Card.Body>\n                            <h5>{t('root_users')}</h5>\n                            <h3>{getRootUsersCount()}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"bg-info text-white\">\n                        <Card.Body>\n                            <h5>{t('expanded_nodes')}</h5>\n                            <h3>{expandedNodes.size}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Header>\n                            <h5 className=\"mb-0\">{t('referral_tree')}</h5>\n                            <small className=\"text-muted\">\n                                {t('click_to_expand_collapse')}\n                            </small>\n                        </Card.Header>\n                        <Card.Body style={{ maxHeight: '600px', overflowY: 'auto' }}>\n                            {filteredTree.length === 0 ? (\n                                <div className=\"text-center text-muted py-4\">\n                                    {searchTerm ? t('no_search_results') : t('no_referral_data')}\n                                </div>\n                            ) : (\n                                <div>\n                                    {filteredTree.map(node => renderTreeNode(node))}\n                                </div>\n                            )}\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default Recommend;\n", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH4 = divWithClassName('h4');\nDivStyledAsH4.displayName = 'DivStyledAsH4';\nconst AlertHeading = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH4,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-heading');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertHeading.displayName = 'AlertHeading';\nexport default AlertHeading;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AlertLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = Anchor,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertLink.displayName = 'AlertLink';\nexport default AlertLink;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AlertHeading from './AlertHeading';\nimport AlertLink from './AlertLink';\nimport Fade from './Fade';\nimport CloseButton from './CloseButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst Alert = /*#__PURE__*/React.forwardRef((uncontrolledProps, ref) => {\n  const {\n    bsPrefix,\n    show = true,\n    closeLabel = 'Close alert',\n    closeVariant,\n    className,\n    children,\n    variant = 'primary',\n    onClose,\n    dismissible,\n    transition = Fade,\n    ...props\n  } = useUncontrolled(uncontrolledProps, {\n    show: 'onClose'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'alert');\n  const handleClose = useEventCallback(e => {\n    if (onClose) {\n      onClose(false, e);\n    }\n  });\n  const Transition = transition === true ? Fade : transition;\n  const alert = /*#__PURE__*/_jsxs(\"div\", {\n    role: \"alert\",\n    ...(!Transition ? props : undefined),\n    ref: ref,\n    className: classNames(className, prefix, variant && `${prefix}-${variant}`, dismissible && `${prefix}-dismissible`),\n    children: [dismissible && /*#__PURE__*/_jsx(CloseButton, {\n      onClick: handleClose,\n      \"aria-label\": closeLabel,\n      variant: closeVariant\n    }), children]\n  });\n  if (!Transition) return show ? alert : null;\n  return /*#__PURE__*/_jsx(Transition, {\n    unmountOnExit: true,\n    ...props,\n    ref: undefined,\n    in: show,\n    children: alert\n  });\n});\nAlert.displayName = 'Alert';\nexport default Object.assign(Alert, {\n  Link: AlertLink,\n  Heading: AlertHeading\n});"], "names": ["Row", "React", "_ref", "ref", "bsPrefix", "className", "as", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "for<PERSON>ach", "brkPoint", "propValue", "cols", "infix", "push", "_jsx", "classNames", "displayName", "Spinner", "variant", "animation", "size", "bsSpinnerPrefix", "Recommend", "t", "useTranslation", "loading", "setLoading", "useState", "referralTree", "setReferralTree", "filteredTree", "setFilteredTree", "expandedNodes", "setExpandedNodes", "Set", "error", "setError", "searchTerm", "setSearchTerm", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "agentProfile", "agent<PERSON><PERSON>r", "from", "select", "eq", "id", "single", "console", "allUsers", "usersError", "order", "ascending", "tree", "buildReferralTree", "err", "fetchReferralData", "trim", "filterTree", "nodes", "filter", "node", "matchesSearch", "email", "toLowerCase", "includes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "length", "map", "users", "userMap", "Map", "rootUsers", "set", "referred_by", "parent", "get", "getRoleIcon", "role", "FaUsers", "FaUser", "renderTreeNode", "level", "arguments", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isExpanded", "has", "paddingLeft", "_jsxs", "style", "cursor", "onClick", "userId", "newExpanded", "delete", "add", "toggleNode", "FaChevronDown", "FaChevronRight", "substring", "child", "Container", "minHeight", "<PERSON><PERSON>", "Col", "Card", "Body", "md", "Form", "Group", "Label", "Control", "type", "placeholder", "value", "onChange", "e", "target", "<PERSON><PERSON>", "expandAll", "allNodeIds", "collectNodeIds", "FaExpandArrowsAlt", "collapseAll", "FaCompressArrowsAlt", "total", "countNodes", "nodeList", "getTotalUsers", "Header", "maxHeight", "overflowY", "DivStyledAsH4", "divWithClassName", "AlertHeading", "AlertLink", "<PERSON><PERSON>", "uncontrolledProps", "show", "<PERSON><PERSON><PERSON><PERSON>", "closeVariant", "onClose", "dismissible", "transition", "Fade", "useUncontrolled", "prefix", "handleClose", "useEventCallback", "Transition", "alert", "CloseButton", "unmountOnExit", "in", "Object", "assign", "Link", "Heading"], "sourceRoot": ""}