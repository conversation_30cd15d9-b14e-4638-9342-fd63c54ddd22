import React, { Suspense, useEffect, useState } from 'react';
import { initSupabase } from './supabaseClient';
import {
  HashRouter,
  Routes,
  Route,
  Link,
  Navigate,
  useLocation,
} from 'react-router-dom';
import { Container, Navbar, Nav, NavDropdown } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { FaTachometerAlt, FaHardHat, FaGlobe, FaCoins, FaChartBar, FaFileInvoiceDollar, FaUsers, FaShoppingBag, FaYenSign } from 'react-icons/fa';

// Lazy load components for better performance
const LoginPage = React.lazy(() => import('./pages/LoginPage'));
const CustomerDashboard = React.lazy(() => import('./pages/customer/Dashboard'));
const ProductListPage = React.lazy(() => import('./pages/customer/ProductListPage'));
const OrderListPage = React.lazy(() => import('./pages/customer/OrderListPage'));
const WalletPage = React.lazy(() => import('./pages/customer/WalletPage'));
const MyAccountPage = React.lazy(() => import('./pages/customer/MyAccountPage'));
const MyGainsPage = React.lazy(() => import('./pages/customer/MyGainsPage'));
const KycPage = React.lazy(() => import('./pages/customer/KycPage'));
const RecommendPage = React.lazy(() => import('./pages/customer/RecommendPage'));
const AgentDashboard = React.lazy(() => import('./pages/agent/Dashboard'));
const AgentMemberList = React.lazy(() => import('./pages/agent/AgentMemberList'));
const AgentProductListPage = React.lazy(() => import('./pages/agent/AgentProductListPage'));
const DebugAgent = React.lazy(() => import('./pages/agent/DebugAgent'));
const MakerDashboard = React.lazy(() => import('./pages/maker/Dashboard'));
const MakerProductListPage = React.lazy(() => import('./pages/maker/MakerProductListPage'));
const MakerOrderListPage = React.lazy(() => import('./pages/maker/MakerOrderListPage'));
const MakerFacilityListPage = React.lazy(() => import('./pages/maker/MakerFacilities'));
const MakerMinerListPage = React.lazy(() => import('./pages/maker/MakerMiners'));
const MinerEarnings = React.lazy(() => import('./pages/maker/MinerEarnings'));
const MinerSnapshots = React.lazy(() => import('./pages/maker/MinerSnapshots'));
const Transactions = React.lazy(() => import('./pages/maker/Transactions'));
const CoinBatches = React.lazy(() => import('./pages/maker/CoinBatches'));
const NetworkStats = React.lazy(() => import('./pages/maker/NetworkStats'));
const CustomerAssets = React.lazy(() => import('./pages/maker/CustomerAssets'));
const OrderReports = React.lazy(() => import('./pages/maker/OrderReports'));
const OrderDistributions = React.lazy(() => import('./pages/maker/OrderDistributions'));
const CapacityRequest = React.lazy(() => import('./pages/maker/CapacityRequest'));
const ManualDeposits = React.lazy(() => import('./pages/maker/ManualDeposits'));
const AgentCapacityRequest = React.lazy(() => import('./pages/agent/CapacityRequest'));
const AgentNetworkStats = React.lazy(() => import('./pages/agent/NetworkStats'));

function App() {
  const { t, i18n } = useTranslation();
  const [supabase, setSupabase] = useState(null);
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const role = localStorage.getItem('user_role'); // 从 localStorage 读取用户角色

  useEffect(() => {
    const initialize = async () => {
      const supa = await initSupabase();
      setSupabase(supa);

      const { data: { session } } = await supa.auth.getSession();
      setSession(session);

      supa.auth.onAuthStateChange((_event, newSession) => {
        setSession(newSession);
        if (!newSession) {
          localStorage.removeItem('user_role');
        }
      });

      setLoading(false);
    };
    initialize();
  }, []);

  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
  };

  // Debug: Log current URL and hash
  React.useEffect(() => {
    console.log('App mounted. Current URL:', window.location.href);
    console.log('Hash:', window.location.hash);
  }, []);

  // Require login to access protected pages
  const RequireAuth = ({ children }) => {
    const location = useLocation();
    if (!session) {
      return <Navigate to="/login" state={{ from: location }} replace />;
    }
    return children;
  };

  // Auto redirect from "/" based on role
  const RoleRedirect = () => {
    const role = localStorage.getItem('user_role');
    if (role === 'maker') return <Navigate to="/maker" replace />;
    if (role === 'agent') return <Navigate to="/agent" replace />;
    if (role === 'customer') return <Navigate to="/customer" replace />;
    return <Navigate to="/login" replace />; // default to login page
  };

  return (
    <HashRouter>
      <div>
        <Navbar bg="dark" variant="dark" expand="lg">
          <Container>
            <Navbar.Toggle aria-controls="basic-navbar-nav" />
            <Navbar.Collapse id="basic-navbar-nav">
              <Nav className="me-auto">
                {/* ===== ★ Maker 导航开始 ★ ===== */}
                {role === 'maker' && (
                  <>
                    {/* Miner Management 下拉 */}
                    <NavDropdown
                      title={
                        <>
                          <FaHardHat className="me-1" />
                          {t('miner_management')}
                        </>
                      }
                      id="maker-miner-dropdown"
                    >
                      <NavDropdown.Item as={Link} to="/maker/miners">
                        {t('miner_list')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/facilities">
                        {t('facility_list')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/earnings">
                        {t('earnings_list')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/transfers">
                        {t('transfer_list')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/snapshots">
                        {t('daily_snapshot')}
                      </NavDropdown.Item>
                    </NavDropdown>

                    <NavDropdown title={
                        <>
                          <FaGlobe className="me-1" />
                          {t('operations_management')}
                        </>
                      }
                      id="maker-operations-dropdown"
                    >
                      <NavDropdown.Item as={Link} to="/maker/capacity">
                        {t('capacity_expansion_request')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/orders">
                        {t('maker_orders')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/manual-deposits">
                        {t('manual_deposit')}
                      </NavDropdown.Item>
                    </NavDropdown>

                    <NavDropdown title={
                        <>
                          <FaCoins className="me-1" />
                          {t('coin_management')}
                        </>
                      }
                      id="maker-coin-dropdown"
                    >
                      <NavDropdown.Item as={Link} to="/maker/coin-batches">
                        {t('coin_batches')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/network-stats">
                        {t('network_stats')}
                      </NavDropdown.Item>
                    </NavDropdown>

                    <NavDropdown title={
                        <>
                          <FaChartBar className="me-1" />
                          {t('report_management')}
                        </>
                      }
                      id="maker-report-dropdown"
                    >
                      <NavDropdown.Item as={Link} to="/maker/customer-assets">
                        {t('customer_assets')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/order-reports">
                        {t('order_reports')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/order-distributions">
                        {t('order_distributions')}
                      </NavDropdown.Item>
                    </NavDropdown>
                  </>
                )}
                {/* ===== ★ Agent 导航开始 ★ ===== */}
                {role === 'agent' && (
                  <>
                    {/* Agent Management 下拉 */}
                    <NavDropdown
                      title={
                        <>
                          <FaGlobe className="me-1" />
                          {t('operational_settings')}
                        </>
                      }
                      id="agent-operational-dropdown"
                    >
                      <NavDropdown.Item as={Link} to="/agent/power">
                        {t('power_records')}
                      </NavDropdown.Item>
                    </NavDropdown>

                    <NavDropdown title={
                        <>
                          <FaFileInvoiceDollar className="me-1" />
                          {t('profit_management')}
                        </>
                      }
                      id="agent-profit-dropdown"
                    >
                      <NavDropdown.Item as={Link} to="/agent/profit">
                        {t('profit_records')}
                      </NavDropdown.Item>
                    </NavDropdown>

                    <NavDropdown title={
                        <>
                          <FaUsers className="me-1" />
                          {t('member_management')}
                        </>
                      }
                      id="agent-member-dropdown"
                    >
                      <NavDropdown.Item as={Link} to="/agent/member-list">
                        {t('member_list')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/agent/recommendation">
                        {t('recommendation')}
                      </NavDropdown.Item>
                    </NavDropdown>

                    <NavDropdown title={
                        <>
                          <FaShoppingBag className="me-1" />
                          {t('product_management')}
                        </>
                      }
                      id="agent-product-dropdown"
                    >
                      <NavDropdown.Item as={Link} to="/agent/product-list">
                        {t('product_list')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/agent/onsale-list">
                        {t('onsale_list')}
                      </NavDropdown.Item>
                    </NavDropdown>

                    <NavDropdown title={
                        <>
                          <FaYenSign className="me-1" />
                          {t('finance_management')}
                        </>
                      }
                      id="agent-finance-dropdown"
                    >
                      <NavDropdown.Item as={Link} to="/agent/wallet-flow-list">
                        {t('wallet_flow')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/agent/withdraw-list">
                        {t('withdraw_list')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/agent/order-list">
                        {t('order_list')}
                      </NavDropdown.Item>
                    </NavDropdown>
                  </>
                )}
                {/* ===== ★ Agent 导航结束 ★ ===== */}
                <Nav.Link href="#/">
                  <FaTachometerAlt className="me-1" />
                  {t('dashboard')}
                </Nav.Link>
                {/* Add other nav links based on role later */}
              </Nav>
              <Nav>
                <NavDropdown title={t('language')} id="basic-nav-dropdown">
                  <NavDropdown.Item onClick={() => changeLanguage('ja')}>日本語</NavDropdown.Item>
                  <NavDropdown.Item onClick={() => changeLanguage('zh')}>中文</NavDropdown.Item>
                  <NavDropdown.Item onClick={() => changeLanguage('en')}>English</NavDropdown.Item>
                </NavDropdown>
              </Nav>
            </Navbar.Collapse>
          </Container>
        </Navbar>

        <Container className="mt-4">
          <Suspense fallback={<div>{t('loading')}</div>}>
            {loading ? (
              <div>{t('initializing_platform')}</div>
            ) : !supabase ? (
              <div className="alert alert-danger">{t('backend_connection_failed')}</div>
            ) : (
              <Routes>
                {/* Public Route */}
                <Route path="/login" element={<LoginPage />} />

                {/* Root path → redirect by role */}
                <Route path="/" element={<RequireAuth><RoleRedirect /></RequireAuth>} />

                {/* Customer Routes */}
                <Route path="/customer" element={<RequireAuth><CustomerDashboard /></RequireAuth>} />
                <Route path="/products" element={<RequireAuth><ProductListPage /></RequireAuth>} />
                <Route path="/orders" element={<RequireAuth><OrderListPage /></RequireAuth>} />
                <Route path="/wallet" element={<RequireAuth><WalletPage /></RequireAuth>} />
                <Route path="/my" element={<RequireAuth><MyAccountPage /></RequireAuth>} />
                <Route path="/my-gains" element={<RequireAuth><MyGainsPage /></RequireAuth>} />
                <Route path="/my/kyc" element={<RequireAuth><KycPage /></RequireAuth>} />
                <Route path="/my/recommend" element={<RequireAuth><RecommendPage /></RequireAuth>} />

                {/* Agent Routes */}
                <Route path="/agent" element={<RequireAuth><AgentDashboard /></RequireAuth>} />
                <Route path="/agent/members" element={<RequireAuth><AgentMemberList /></RequireAuth>} />
                <Route path="/agent/products" element={<RequireAuth><AgentProductListPage /></RequireAuth>} />
                <Route path="/agent/debug" element={<RequireAuth><DebugAgent /></RequireAuth>} />
                <Route path="/agent/power" element={<RequireAuth><AgentCapacityRequest /></RequireAuth>} />
                <Route path="/agent/profit" element={<RequireAuth><AgentNetworkStats /></RequireAuth>} />

                {/* Maker Routes */}
                  <Route path="/maker" element={<RequireAuth><MakerDashboard /></RequireAuth>} />
                  <Route path="/maker/products" element={<RequireAuth><MakerProductListPage /></RequireAuth>} />
                  <Route path="/maker/orders" element={<RequireAuth><MakerOrderListPage /></RequireAuth>} />
                  <Route path="/maker/facilities" element={<RequireAuth><MakerFacilityListPage /></RequireAuth>} />
                  <Route path="/maker/miners" element={<RequireAuth><MakerMinerListPage /></RequireAuth>} />
                  <Route path="/maker/earnings" element={<RequireAuth><MinerEarnings /></RequireAuth>} />
                  <Route path="/maker/transfers" element={<RequireAuth><Transactions /></RequireAuth>} />
                  <Route path="/maker/snapshots" element={<RequireAuth><MinerSnapshots /></RequireAuth>} />
                  <Route path="/maker/coin-batches" element={<RequireAuth><CoinBatches /></RequireAuth>} />
                  <Route path="/maker/network-stats" element={<RequireAuth><NetworkStats /></RequireAuth>} />
                  <Route path="/maker/customer-assets" element={<RequireAuth><CustomerAssets /></RequireAuth>} />
                  <Route path="/maker/order-reports" element={<RequireAuth><OrderReports /></RequireAuth>} />
                  <Route path="/maker/order-distributions" element={<RequireAuth><OrderDistributions /></RequireAuth>} />
                  <Route path="/maker/capacity" element={<RequireAuth><CapacityRequest /></RequireAuth>} />
                  <Route path="/maker/manual-deposits" element={<RequireAuth><ManualDeposits /></RequireAuth>} />
                
                {/* Fallback */}
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            )}
          </Suspense>
        </Container>
      </div>
    </HashRouter>
  );
}

export default App;
