import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { getSupabase } from '../../supabaseClient';

const AgentDashboard = () => {
    const { t } = useTranslation();
    const [agentProfile, setAgentProfile] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchAgentData = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                console.log('No user found');
                setLoading(false);
                return; // User not logged in
            }

            console.log('Current user:', user.id);
            console.log('User role from localStorage:', localStorage.getItem('user_role'));

            // Fetch agent profile
            const { data: profileData, error: profileError } = await supabase
                .from('agent_profiles')
                .select('*')
                .eq('user_id', user.id)
                .single();

            if (profileError) {
                console.error('Error fetching agent profile:', profileError);
                console.error('User ID:', user.id);
                console.error('Error details:', profileError);
            } else {
                console.log('Agent profile data:', profileData);
                setAgentProfile(profileData);
            }
            setLoading(false);
        };

        fetchAgentData();
    }, []);

    if (loading) {
        return <div>{t('loading_agent_dashboard')}</div>;
    }

    if (!agentProfile) {
        return <div className="alert alert-warning">{t('not_agent')}</div>;
    }

    return (
        <Container fluid>
            <Row className="mb-3">
                <Col>
                    <h2>{t('agent_dashboard')}</h2>
                </Col>
            </Row>

            <Row>
                <Col md={4}>
                    <Card className="bg-primary text-white mb-3">
                        <Card.Body>
                            <Card.Title>{t('brand_name')}</Card.Title>
                            <h3>{agentProfile.brand_name || 'N/A'}</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={4}>
                    <Card className="bg-success text-white mb-3">
                        <Card.Body>
                            <Card.Title>{t('commission_rate')}</Card.Title>
                            <h3>{agentProfile.commission_pct ? `${agentProfile.commission_pct * 100}%` : 'N/A'}</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={4}>
                    <Card className="bg-info text-white mb-3">
                        <Card.Body>
                            <Card.Title>{t('kyc_status')}</Card.Title>
                            <h3>{agentProfile.kyc_status || 'N/A'}</h3>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row className="mt-4">
                <Col md={6} className="text-center">
                    <Card>
                        <Card.Body>
                            <h4>{t('member_management')}</h4>
                            <p>{t('my_subordinate_members')}</p>
                            <Button as={Link} to="/agent/members" variant="primary">{t('enter_member_list')}</Button>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6} className="text-center">
                    <Card>
                        <Card.Body>
                            <h4>{t('product_management')}</h4>
                            <p>{t('products_on_sale')}</p>
                            <Button as={Link} to="/agent/products" variant="success">{t('browse_agent_products')}</Button>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default AgentDashboard;